'use client';

import { useEffect, useState } from 'react';
import {
  Clock, UploadCloud, Link as LinkIcon,
  Code2, Database, ShieldCheck
} from 'lucide-react';
import pbstudent from '@/lib/db';

export default function AssignmentPage() {
  const [activeTab, setActiveTab] = useState('Pending');
  const [assignments, setAssignments] = useState([]);

  const iconMap = {
    'Web Dev': <Code2 className="text-white" />,
    'Database': <Database className="text-white" />,
    'Security': <ShieldCheck className="text-white" />,
  };

  const colorMap = {
    'Web Dev': 'purple',
    'Database': 'pink',
    'Security': 'purple',
  };

  useEffect(() => {
    const fetchAssignments = async () => {
      const studentId = pbstudent.authStore.model?.id;
      if (!studentId) return;

      try {
        const results = await pbstudent.collection('assignments').getFullList({
          filter: `student="${studentId}" && status="${activeTab}"`,
          sort: 'due_date',
        });

        setAssignments(results);
      } catch (error) {
        console.log("Error fetching assignments:", error);
      }
    };

    fetchAssignments();
  }, [activeTab]);

  const tabs = ['Pending', 'Submitted', 'Graded'];

  return (
    <div className="flex min-h-screen bg-background font-sans">
      <div className="flex-1 p-6">

        {/* Tabs */}
        <div className="mt-6">
          <div className="flex gap-6 border-b  text-sm text-light-primary">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`pb-2 border-b-2 transition-colors ${activeTab === tab ? 'border-foreground text-foreground font-semibold' : 'border-transparent'}`}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Assignment Cards */}
        <div className="grid grid-cols-3 gap-5 mt-6">
          {assignments.map((item, idx) => (
            <div key={idx} className="bg-white rounded-xl shadow p-5">
              <div className="flex items-center gap-3 mb-2">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${idx % 2 === 0 ? 'bg-foreground' : 'bg-primary'}`}>
                  {iconMap[item.subject] || <Code2 className="text-white" />}
                </div>
                <h3 className="text-base font-semibold text-black">{item.title}</h3>
              </div>
              <div className="flex items-center text-xs text-light-primary/80 mb-2">
                <Clock size={14} className="mr-1" />
                Due on {new Date(item.due_date).toLocaleDateString()}
              </div>
              <p className="text-sm text-light-primary mb-3">{item.desc}</p>
              <div className="flex items-center justify-between text-sm mb-3">
                <div className={`flex items-center gap-1 cursor-pointer justify-center ${idx % 2 === 0 ? 'text-foreground' : 'text-primary'}`}>
                  <LinkIcon size={14} /> View Resources
                </div>
                <span className="text-gray-500">Marks: {item.marks}</span>
              </div>
              <button className={`w-full ${idx % 2 === 0 ? 'bg-foreground hover:bg-foreground/90' : 'bg-primary hover:bg-primary/90'} text-white py-2 rounded-lg text-sm flex items-center justify-center gap-2`}>
                <UploadCloud size={16} /> Upload Assignment
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

