module.exports = {
  darkMode: 'class',
  content: ['./app/**/*.{js,jsx}', './components/**/*.{js,jsx}'],
  theme: {
    extend: {
      colors: {
        'background-dark': '#0f172a',
        'light-primary': '#6B7280',
      },
    },
  },
  plugins: [],
};
















// /** @type {import('tailwindcss').Config} */
// module.exports = {
//   darkMode: 'class', // Enable class-based dark mode
//   content: [
//     './src/**/*.{js,jsx,ts,tsx}', // Scan all your src files
//   ],
//   theme: {
//     extend: {
//       colors: {
//         // Optional: your custom theme colors
//         primary: '#6b21a8',
//         background: '#ffffff',
//         'background-dark': '#0f172a',
//         foreground: '#1e293b',
//       },
//     },
//   },
//   plugins: [],
// };



